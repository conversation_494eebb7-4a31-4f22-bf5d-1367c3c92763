import express from "express";
import protectRoute from "../middlewares/protectRoute.js";
import { getMessages, sendMessage, getConversations, deleteMessage, uploadAttachment } from "../controllers/messageController.js";
import { uploadMixedMedia, handleMulterError } from "../middlewares/multerConfig.js";

const router = express.Router();

router.get("/conversations", protectRoute, getConversations);
router.get("/:otherUserId", protectRoute, getMessages);
router.post("/", protectRoute, uploadMixedMedia, handleMulterError, sendMessage);
router.delete("/:messageId", protectRoute, deleteMessage);

// Paperclip attachment upload
router.post("/upload-attachment", protectRoute, uploadMixedMedia, handleMulterError, uploadAttachment);

export default router;
