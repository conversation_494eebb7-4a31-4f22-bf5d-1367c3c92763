import {
	Flex,
	Input,
	InputGroup,
	InputRightElement,
	InputLeftElement,
	Modal,
	ModalBody,
	ModalCloseButton,
	ModalContent,
	ModalHeader,
	ModalOverlay,
	Spinner,
	useDisclosure,
	Tooltip,
	Box,
	Text,
	Button,
	Icon,
	IconButton,
	Divider,
	Progress,
	Popover,
	PopoverTrigger,
	PopoverContent,
	PopoverBody,
	Portal,
	useColorModeValue,
	Textarea,
	HStack,
	VStack,
} from "@chakra-ui/react";
import { IoSendSharp, IoAttach, IoMicOutline, IoHappyOutline, IoImageOutline } from "react-icons/io5";
import { FaPaperclip, FaSmile, FaMicrophone } from "react-icons/fa";
import { MdEmojiEmotions, MdAttachFile } from "react-icons/md";
import { CloseIcon } from "@chakra-ui/icons";
import useShowToast from "../hooks/useShowToast";
import { useRecoilValue, useSetRecoilState } from "recoil";
import { user<PERSON>tom, conversations<PERSON><PERSON>, selected<PERSON>onversation<PERSON><PERSON> } from "../atoms";
import usePreviewImg from "../hooks/usePreviewImg";
import { useSocket } from "../hooks/useSocket";
import { memo, useRef, useState, useCallback, useEffect } from "react";
import { fetchWithSession } from "../utils/api";
import "../styles/telegram-input.css";


const MessageInput = memo(({ setMessages }) => {
	const [messageText, setMessageText] = useState("");
	const [isRecording, setIsRecording] = useState(false);
	const [showAttachMenu, setShowAttachMenu] = useState(false);
	const [textareaHeight, setTextareaHeight] = useState(40);
	const textareaRef = useRef(null);
	const fileInputRef = useRef(null);
	const showToast = useShowToast();
	const selectedConversation = useRecoilValue(selectedConversationAtom);
	const setConversations = useSetRecoilState(conversationsAtom);
	const currentUser = useRecoilValue(userAtom);
	const { socket, isConnected } = useSocket();
	const { isOpen: isImageOpen, onOpen: onImageOpen, onClose: onImageClose } = useDisclosure();
	const { isOpen: isEmojiOpen, onOpen: onEmojiOpen, onClose: onEmojiClose } = useDisclosure();
	const { handleImageChange, imgUrl, setImgUrl } = usePreviewImg();
	const [isSending, setIsSending] = useState(false);
	const [selectedEmoji, setSelectedEmoji] = useState("");

	// Theme-aware colors
	const inputBgColor = useColorModeValue("#f8f9fa", "#2d2d2d");
	const inputBorderColor = useColorModeValue("#e0e0e0", "#404040");
	const textColor = useColorModeValue("gray.800", "white");
	const placeholderColor = useColorModeValue("gray.500", "gray.400");
	const buttonHoverBg = useColorModeValue("gray.100", "whiteAlpha.100");
	const containerBg = useColorModeValue("white", "#1a1a1a");
	const borderTopColor = useColorModeValue("gray.200", "gray.700");
	const emojiPickerTextColor = useColorModeValue("gray.800", "whiteAlpha.900");
	const menuButtonColor = useColorModeValue("gray.600", "whiteAlpha.800");
	const menuButtonHoverBg = useColorModeValue("rgba(59, 130, 246, 0.1)", "rgba(59, 130, 246, 0.1)");
	const menuButtonHoverColor = useColorModeValue("#3b82f6", "#3b82f6");

	// Auto-resize textarea
	useEffect(() => {
		if (textareaRef.current) {
			textareaRef.current.style.height = 'auto';
			const scrollHeight = textareaRef.current.scrollHeight;
			const newHeight = Math.min(Math.max(scrollHeight, 40), 120); // Min 40px, max 120px
			setTextareaHeight(newHeight);
			textareaRef.current.style.height = `${newHeight}px`;
		}
	}, [messageText]);



	// Handle file attachment
	const handleFileAttachment = useCallback(() => {
		console.log("📎 File attachment clicked");
		try {
			const input = document.createElement('input');
			input.type = 'file';
			input.accept = '*/*';
			input.style.display = 'none';

			input.onchange = async (e) => {
				const file = e.target.files[0];
				console.log("📎 File selected:", file);
				if (file) {
					try {
						console.log("📎 Uploading file...");
						const formData = new FormData();
						formData.append('file', file);

						const response = await fetchWithSession('/api/messages/upload-attachment', {
							method: 'POST',
							body: formData
						});

						const result = await response.json();
						if (result.success) {
							// Send message with file
							const messageData = {
								recipientId: selectedConversation.userId,
								text: `📎 ${result.fileName}`,
								file: result.fileUrl,
								fileName: result.fileName,
								fileSize: result.fileSize
							};

							await fetchWithSession('/api/messages', {
								method: 'POST',
								headers: { 'Content-Type': 'application/json' },
								body: JSON.stringify(messageData)
							});

							showToast("Success", "File sent successfully!", "success");
							setShowAttachMenu(false); // Close the attachment menu
						}
					} catch (error) {
						console.error("📎 File upload error:", error);
						showToast("Error", "Failed to upload file", "error");
					}
				}
				// Clean up
				document.body.removeChild(input);
			};

			// Add to DOM temporarily and click
			document.body.appendChild(input);
			input.click();
		} catch (error) {
			console.error("📎 File attachment error:", error);
			showToast("Error", "Failed to open file picker", "error");
		}
	}, [selectedConversation?.userId, showToast]);

	// Handle image attachment
	const handleImageAttachment = useCallback(() => {
		console.log("📷 Image attachment clicked");
		try {
			const input = document.createElement('input');
			input.type = 'file';
			input.accept = 'image/*';
			input.style.display = 'none';

			input.onchange = async (e) => {
				const file = e.target.files[0];
				console.log("📷 Image selected:", file);
				if (file) {
					try {
						console.log("📷 Uploading image...");
						const formData = new FormData();
						formData.append('image', file);

						const response = await fetchWithSession('/api/messages/upload-attachment', {
							method: 'POST',
							body: formData
						});

						const result = await response.json();
						if (result.success) {
							setImgUrl(result.imageUrl);
							setShowAttachMenu(false); // Close the attachment menu
							showToast("Success", "Image uploaded! Type a message and send.", "success");
						}
					} catch (error) {
						console.error("📷 Image upload error:", error);
						showToast("Error", "Failed to upload image", "error");
					}
				}
				// Clean up
				document.body.removeChild(input);
			};

			// Add to DOM temporarily and click
			document.body.appendChild(input);
			input.click();
		} catch (error) {
			console.error("📷 Image attachment error:", error);
			showToast("Error", "Failed to open image picker", "error");
		}
	}, [setImgUrl, showToast]);

	// Store mediaRecorder reference
	const mediaRecorderRef = useRef(null);
	const streamRef = useRef(null);

	// Handle voice recording
	const handleVoiceRecording = useCallback(async () => {
		console.log("🎤 Voice recording clicked, isRecording:", isRecording);

		if (!isRecording) {
			// Start recording
			try {
				console.log("🎤 Starting voice recording...");

				// Check if browser supports getUserMedia
				if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
					showToast("Error", "Voice recording not supported in this browser", "error");
					return;
				}

				const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
				streamRef.current = stream;

				const mediaRecorder = new MediaRecorder(stream);
				mediaRecorderRef.current = mediaRecorder;
				const chunks = [];

				mediaRecorder.ondataavailable = (e) => {
					console.log("🎤 Data available:", e.data.size, "bytes");
					chunks.push(e.data);
				};

				mediaRecorder.onstop = async () => {
					console.log("🎤 Recording stopped, processing...");
					const blob = new Blob(chunks, { type: 'audio/wav' });
					console.log("🎤 Created blob:", blob.size, "bytes");

					const formData = new FormData();
					formData.append('voice', blob, 'voice.wav');

					try {
						console.log("🎤 Uploading voice...");
						const response = await fetchWithSession('/api/messages/upload-attachment', {
							method: 'POST',
							body: formData
						});

						const result = await response.json();
						console.log("🎤 Upload result:", result);

						if (result.success) {
							// Send voice message
							const messageData = {
								recipientId: selectedConversation.userId,
								text: "🎤 Voice message",
								voice: result.voiceUrl
							};

							await fetchWithSession('/api/messages', {
								method: 'POST',
								headers: { 'Content-Type': 'application/json' },
								body: JSON.stringify(messageData)
							});

							showToast("Success", "Voice message sent!", "success");
						}
					} catch (error) {
						console.error("🎤 Voice upload error:", error);
						showToast("Error", "Failed to send voice message", "error");
					}

					// Clean up
					if (streamRef.current) {
						streamRef.current.getTracks().forEach(track => track.stop());
						streamRef.current = null;
					}
					mediaRecorderRef.current = null;
				};

				mediaRecorder.start();
				setIsRecording(true);
				console.log("🎤 Recording started");

				// Auto-stop after 60 seconds
				setTimeout(() => {
					if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
						console.log("🎤 Auto-stopping after 60 seconds");
						mediaRecorderRef.current.stop();
						setIsRecording(false);
					}
				}, 60000);

			} catch (error) {
				console.error("🎤 Microphone access error:", error);
				setIsRecording(false);
				if (error.name === 'NotAllowedError') {
					showToast("Error", "Microphone permission denied. Please allow microphone access.", "error");
				} else if (error.name === 'NotFoundError') {
					showToast("Error", "No microphone found on this device.", "error");
				} else {
					showToast("Error", "Could not access microphone: " + error.message, "error");
				}
			}
		} else {
			// Stop recording
			console.log("🎤 Manually stopping voice recording...");
			if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
				mediaRecorderRef.current.stop();
			}
			setIsRecording(false);
		}
	}, [isRecording, selectedConversation?.userId, showToast]);

	// Emoji list (simplified for demo)
	const emojiList = ["😀", "😂", "😍", "🤔", "😢", "😡", "👍", "👎", "❤️", "🔥", "✨", "🎉"];

	// Define sendRequestFn to handle both regular and federated messages
	const sendRequestFn = async (formData, messageData) => {
		let responseData = null;

		// Handle federated messages
		if (selectedConversation.isFederated) {
			console.log("Sending federated message:", messageData.text);
			const response = await fetchWithSession("/api/cross-platform/rooms/" + selectedConversation._id + "/messages", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					message: messageData.text
				}),
			});
			responseData = await response.json();
			console.log("Federated message response:", responseData);

			// For federated messages, transform the response to match expected format
			if (responseData.success && responseData.localMessage) {
				return {
					_id: responseData.localMessage.id,
					text: responseData.localMessage.text,
					sender: responseData.localMessage.sender._id,
					senderUsername: responseData.localMessage.sender.username,
					senderPlatform: responseData.localMessage.sender.platform,
					createdAt: responseData.localMessage.timestamp,
					isFederated: true,
					platform: responseData.localMessage.platform,
					tempId: messageData.tempId
				};
			}
		} else {
			// Handle regular messages
			if (formData) {
				const response = await fetchWithSession("/api/messages", {
					method: "POST",
					body: formData,
				});
				responseData = await response.json();
			} else {
				const response = await fetchWithSession("/api/messages", {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
					},
					body: JSON.stringify(messageData),
				});
				responseData = await response.json();
			}
		}

		console.log("Message sent successfully (server response):", responseData);
		return responseData;
	};

	// PATCH: Define messageAlreadyUpdated utility
	const messageAlreadyUpdated = (prev, tempId, responseData) => {
		return prev.some(msg =>
			(msg.tempId === tempId && !msg.isOptimistic) ||
			(responseData._id && msg._id === responseData._id)
		);
	};

	// Handle message submission
	const handleSendMessage = useCallback(async (e) => {
		e?.preventDefault();
		if (isSending) return;
		if (!messageText.trim() && !imgUrl && !selectedEmoji) {
			console.warn('Attempted to send empty message. Aborting.');
			return;
		}
		setIsSending(true);
		try {
			const tempId = Date.now().toString();
			let formData = null;
			let messageData = {
				tempId,
				text: messageText,
				recipientId: selectedConversation.userId,
				img: imgUrl || undefined,
				emoji: selectedEmoji || undefined,
			};

			// For federated messages, don't use images/emojis for now
			if (selectedConversation.isFederated && (imgUrl || selectedEmoji)) {
				showToast("Info", "Images and emojis are not supported in cross-platform rooms yet", "info");
				setIsSending(false);
				return;
			}
			// Prepare message with any media type
			if (imgUrl) {
				formData = new FormData();
				formData.append("text", messageText);
				formData.append("recipientId", selectedConversation.userId);
				if (imgUrl && imgUrl.startsWith("blob:")) {
					const response = await fetch(imgUrl);
					const imgBlob = await response.blob();
					formData.append("img", imgBlob);
				}
				if (selectedEmoji) {
					formData.append("emoji", selectedEmoji);
				}
			}
			const optimisticMessage = selectedConversation.isFederated ? {
				_id: tempId,
				text: messageText,
				sender: currentUser._id,
				senderUsername: currentUser.name || currentUser.username,
				senderPlatform: 'sociality',
				createdAt: new Date().toISOString(),
				isOptimistic: true,
				isNew: true,
				isFederated: true,
				platform: 'sociality',
				tempId
			} : {
				text: messageText,
				sender: currentUser._id,
				tempId,
				createdAt: new Date().toISOString(),
				isOptimistic: true,
				isNew: true,
				img: imgUrl || undefined,
				emoji: selectedEmoji || undefined,
			};
			setMessages(prev => [...prev, optimisticMessage]);

			// Immediate aggressive scroll trigger for optimistic message
			const forceImmediateScroll = () => {
				const messageContainer = document.getElementById('messageListContainer');
				if (messageContainer) {
					console.log('📤 Immediate aggressive scroll after sending message');
					// Force scroll to absolute maximum
					messageContainer.scrollTop = messageContainer.scrollHeight;
					console.log('📤 Set scrollTop to:', messageContainer.scrollTop);
				}
			};

			// Multiple immediate scroll attempts
			setTimeout(forceImmediateScroll, 10);
			setTimeout(forceImmediateScroll, 50);
			setTimeout(forceImmediateScroll, 100);

			try {
				console.log("Waiting for server to process message...");
				const responseData = await sendRequestFn(formData, messageData);
				setMessages(prev => {
					if (messageAlreadyUpdated(prev, tempId, responseData)) {
						console.log("Message already updated by socket, skipping update");
						return prev;
					}
					const updatedMessages = prev.map(msg =>
						msg.tempId === tempId ? { ...responseData, isNew: true } : msg
					);

					return updatedMessages;
				});
				setConversations(prev => {
					const updatedConversations = [...prev];
					const conversationIndex = updatedConversations.findIndex(c => c._id === selectedConversation._id);
					if (conversationIndex !== -1) {
						updatedConversations[conversationIndex] = {
							...updatedConversations[conversationIndex],
							lastMessage: {
								text: messageText,
								sender: currentUser._id,
								img: imgUrl ? true : undefined,
								emoji: selectedEmoji || undefined,
							},
						};
						const conversation = updatedConversations.splice(conversationIndex, 1)[0];
						updatedConversations.unshift(conversation);
					}
					return updatedConversations;
				});


			} catch (error) {
				showToast("Error", error.message, "error");
			}
			document.getElementById('messageInput')?.blur();
			setMessageText("");
			setImgUrl("");
			setSelectedEmoji("");
		} catch (error) {
			showToast("Error", error.message, "error");
		} finally {
			setIsSending(false);
		}
	}, [
		messageText,
		imgUrl,
		selectedEmoji,
		selectedConversation?.userId,
		currentUser?._id,
		setMessages,
		showToast,
		setImgUrl,
		setConversations
	]);

	// Handle Enter key press
	const handleKeyDown = useCallback((e) => {
		if (e.key === 'Enter' && !e.shiftKey) {
			e.preventDefault();
			console.log('Enter key pressed, message text:', messageText);
			handleSendMessage(e);
		}
	}, [handleSendMessage, messageText]);

	return (
		<Box
			bg={containerBg}
			p={2} // Reduced padding to minimize extra space
		>
			{/* Image preview */}
			{imgUrl && (
				<Box mb={3} className="telegram-image-preview">
					<HStack spacing={3}>
						<Box
							w="60px"
							h="60px"
							borderRadius="md"
							overflow="hidden"
							bg={useColorModeValue("gray.200", "gray.100")}
							backgroundImage={`url(${imgUrl})`}
							backgroundSize="cover"
							backgroundPosition="center"
						/>
						<VStack align="start" spacing={1} flex={1}>
							<Text fontSize="sm" fontWeight="medium" color={textColor}>
								Image Preview
							</Text>
							<Text fontSize="xs" color={useColorModeValue("gray.600", "gray.500")}>
								Image ready to send
							</Text>
						</VStack>
						<IconButton
							icon={<CloseIcon />}
							size="sm"
							variant="ghost"
							onClick={() => setImgUrl("")}
							aria-label="Remove image"
						/>
					</HStack>
				</Box>
			)}

			<Flex gap={2} alignItems="flex-end">
				{/* Attachment button */}
				<Popover
					isOpen={showAttachMenu}
					onClose={() => setShowAttachMenu(false)}
					placement="top-start"
				>
					<PopoverTrigger>
						<IconButton
							icon={<MdAttachFile />}
							size="lg"
							variant="ghost"
							colorScheme="gray"
							borderRadius="full"
							onClick={(e) => {
								e.preventDefault();
								e.stopPropagation();
								console.log("📎 Paperclip button clicked, current state:", showAttachMenu);
								setShowAttachMenu(!showAttachMenu);
							}}
							className="telegram-attach-button"
							aria-label="Attach file"
							h="44px"
							w="44px"
						/>
					</PopoverTrigger>
					<Portal>
						<PopoverContent
							w="200px"
							className="telegram-attachment-menu"
						>
							<PopoverBody p={2}>
								<VStack spacing={1}>
									<Button
										leftIcon={<IoImageOutline />}
										variant="ghost"
										size="sm"
										w="full"
										justifyContent="flex-start"
										onClick={(e) => {
											e.preventDefault();
											e.stopPropagation();
											console.log("📷 Photo menu item clicked");
											handleImageAttachment();
										}}
										_hover={{ bg: menuButtonHoverBg, color: menuButtonHoverColor }}
										color={menuButtonColor}
									>
										Photo
									</Button>
									<Button
										leftIcon={<FaPaperclip />}
										variant="ghost"
										size="sm"
										w="full"
										justifyContent="flex-start"
										onClick={(e) => {
											e.preventDefault();
											e.stopPropagation();
											console.log("📁 File menu item clicked");
											handleFileAttachment();
										}}
										_hover={{ bg: menuButtonHoverBg, color: menuButtonHoverColor }}
										color={menuButtonColor}
									>
										File
									</Button>
								</VStack>
							</PopoverBody>
						</PopoverContent>
					</Portal>
				</Popover>

				{/* Main input area */}
				<Box
					flex={1}
					className="telegram-input-container"
					borderRadius="24px"
					p={2}
				>
					<HStack spacing={2} align="flex-end">
						{/* Emoji button */}
						<Popover
							isOpen={isEmojiOpen}
							onClose={onEmojiClose}
							placement="top-start"
							closeOnBlur={true}
							closeOnEsc={true}
						>
							<PopoverTrigger>
								<IconButton
									icon={<MdEmojiEmotions />}
									size="sm"
									variant="ghost"
									colorScheme="gray"
									borderRadius="full"
									onClick={(e) => {
										e.preventDefault();
										e.stopPropagation();
										console.log("😊 Emoji button clicked, current state:", isEmojiOpen);
										if (isEmojiOpen) {
											onEmojiClose();
										} else {
											onEmojiOpen();
										}
									}}
									className="telegram-emoji-button"
									aria-label="Add emoji"
								/>
							</PopoverTrigger>
							<Portal>
								<PopoverContent
									w="280px"
									className="telegram-emoji-picker"
								>
									<PopoverBody p={3}>
										<Text fontSize="sm" fontWeight="medium" mb={2} color={emojiPickerTextColor}>
											Quick Emojis
										</Text>
										<Flex wrap="wrap" gap={2}>
											{emojiList.map((emoji, index) => (
												<Button
													key={index}
													variant="ghost"
													size="sm"
													fontSize="lg"
													onClick={async (e) => {
														e.preventDefault();
														e.stopPropagation();
														console.log("😊 Emoji clicked:", emoji);

														// If message is empty, send emoji directly
														if (!messageText.trim()) {
															try {
																console.log("😊 Sending emoji directly");
																const messageData = {
																	recipientId: selectedConversation.userId,
																	emoji: emoji,
																	text: ""
																};

																await fetchWithSession('/api/messages', {
																	method: 'POST',
																	headers: { 'Content-Type': 'application/json' },
																	body: JSON.stringify(messageData)
																});

																onEmojiClose();
																showToast("Success", "Emoji sent!", "success");
															} catch (error) {
																console.error("😊 Emoji send error:", error);
																showToast("Error", "Failed to send emoji", "error");
															}
														} else {
															// Add to existing text
															console.log("😊 Adding emoji to text");
															setMessageText(prev => prev + emoji);
															onEmojiClose();
														}
													}}
													_hover={{ bg: useColorModeValue("gray.100", "rgba(255, 255, 255, 0.1)") }}
													borderRadius="md"
													p={2}
													color={useColorModeValue("gray.700", "white")}
												>
													{emoji}
												</Button>
											))}
										</Flex>
									</PopoverBody>
								</PopoverContent>
							</Portal>
						</Popover>

						{/* Text input */}
						<Textarea
							ref={textareaRef}
							value={messageText}
							onChange={(e) => setMessageText(e.target.value)}
							onKeyDown={handleKeyDown}
							placeholder="Type a message..."
							className="telegram-textarea"
							minH="40px"
							maxH="120px"
							color={textColor}
							_placeholder={{ color: placeholderColor }}
							fontSize="15px"
							lineHeight="1.4"
							py={2}
							px={1}
							style={{ height: `${textareaHeight}px` }}
						/>
					</HStack>
				</Box>

				{/* Send/Voice button */}
				{messageText.trim() || imgUrl ? (
					<IconButton
						icon={isSending ? <Spinner size="sm" /> : <IoSendSharp />}
						size="lg"
						borderRadius="full"
						onClick={handleSendMessage}
						isDisabled={isSending}
						className="telegram-send-button"
						aria-label="Send message"
						h="44px"
						w="44px"
					/>
				) : (
					<IconButton
						icon={<FaMicrophone />}
						size="lg"
						borderRadius="full"
						onClick={(e) => {
							e.preventDefault();
							e.stopPropagation();
							console.log("🎤 Voice button clicked");
							handleVoiceRecording();
						}}
						className={`telegram-voice-button ${isRecording ? 'recording' : ''}`}
						aria-label={isRecording ? "Stop recording" : "Record voice message"}
						h="44px"
						w="44px"
					/>
				)}
			</Flex>

			{/* Voice recording indicator */}
			{isRecording && (
				<Box
					position="fixed"
					top="50%"
					left="50%"
					transform="translate(-50%, -50%)"
					bg="rgba(0, 0, 0, 0.8)"
					color="white"
					p={4}
					borderRadius="lg"
					zIndex={9999}
					textAlign="center"
				>
					<VStack spacing={2}>
						<Box
							w="40px"
							h="40px"
							borderRadius="full"
							bg="red.500"
							display="flex"
							alignItems="center"
							justifyContent="center"
							animation="pulse 1s infinite"
						>
							<FaMicrophone />
						</Box>
						<Text fontSize="sm">Recording voice message...</Text>
						<Text fontSize="xs" opacity={0.7}>Tap to stop</Text>
					</VStack>
				</Box>
			)}

			{/* Hidden file input */}
			<input
				ref={fileInputRef}
				type="file"
				style={{ display: 'none' }}
				onChange={handleImageChange}
				accept="*/*"
			/>
		</Box>
	);
});

MessageInput.displayName = 'MessageInput';

export default MessageInput;
