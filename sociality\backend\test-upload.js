// Simple test to check if upload endpoint works
import fetch from 'node-fetch';
import FormData from 'form-data';
import fs from 'fs';

// Create a simple test file
const testContent = 'Hello, this is a test file!';
fs.writeFileSync('test.txt', testContent);

async function testUpload() {
    try {
        console.log('🧪 Testing upload endpoint...');
        
        const formData = new FormData();
        formData.append('file', fs.createReadStream('test.txt'));
        
        const response = await fetch('http://localhost:5000/api/messages/upload-attachment', {
            method: 'POST',
            body: formData,
            headers: {
                // Add any auth headers if needed
                'Cookie': 'your-auth-cookie-here'
            }
        });
        
        const result = await response.text();
        console.log('📤 Response status:', response.status);
        console.log('📤 Response:', result);
        
        // Cleanup
        fs.unlinkSync('test.txt');
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        // Cleanup on error
        if (fs.existsSync('test.txt')) {
            fs.unlinkSync('test.txt');
        }
    }
}

// Run test
testUpload();
