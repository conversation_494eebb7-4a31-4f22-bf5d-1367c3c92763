import multer from "multer";

// Configure multer for memory storage (we'll upload to Cloudinary)
const storage = multer.memoryStorage();

// Configure multer for mixed media uploads (images, files, etc.)
export const uploadMixedMedia = multer({
	storage: storage,
	limits: {
		fileSize: 50 * 1024 * 1024, // 50MB limit
		files: 10 // Maximum 10 files at once
	}
}).fields([
	{ name: 'image', maxCount: 1 },
	{ name: 'images', maxCount: 5 },
	{ name: 'file', maxCount: 1 },
	{ name: 'voice', maxCount: 1 },
	{ name: 'gif', maxCount: 1 }
]);

// Error handling middleware for multer
export const handleMulterError = (error, req, res, next) => {
	if (error instanceof multer.MulterError) {
		if (error.code === 'LIMIT_FILE_SIZE') {
			return res.status(400).json({ 
				error: 'File too large. Maximum size allowed is 50MB.' 
			});
		}
		if (error.code === 'LIMIT_FILE_COUNT') {
			return res.status(400).json({ 
				error: 'Too many files. Maximum 5 images or 1 file allowed.' 
			});
		}
		if (error.code === 'LIMIT_UNEXPECTED_FILE') {
			return res.status(400).json({ 
				error: 'Unexpected file field.' 
			});
		}
		return res.status(400).json({ 
			error: `Upload error: ${error.message}` 
		});
	}
	
	if (error && error.message === 'Only image files are allowed!') {
		return res.status(400).json({ 
			error: 'Only image files are allowed for image uploads.' 
		});
	}
	
	next(error);
};

export default {
	uploadMixedMedia,
	handleMulterError
};
